"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Copy, RotateCcw, Trash2, Eye, EyeOff } from "lucide-react"
import { PhonebookKey } from "@/lib/phonebook-keys"
import { formatDistanceToNow } from "date-fns"
import { DeleteKeyDialog } from "./delete-key-dialog"
import { RegenerateSecretDialog } from "./regenerate-secret-dialog"
import { useToast } from "@/hooks/use-toast"

interface PhonebookKeysTableProps {
  keys: PhonebookKey[]
}

interface KeyRowProps {
  keyData: PhonebookKey
  onDelete: (keyData: PhonebookKey) => void
  onRegenerate: (keyData: PhonebookKey) => void
  onCopy: (text: string, type: 'key' | 'secret') => void
}

function KeyRow({ keyData, onDelete, onRegenerate, onCopy }: KeyRowProps) {
  const [showSecret, setShowSecret] = useState(false)

  const formatKey = (key: string) => {
    if (key.length <= 12) return key
    return `${key.slice(0, 8)}...${key.slice(-4)}`
  }

  const formatSecret = (secret: string) => {
    if (showSecret) return secret
    return "••••••••••••••••••••••••••••••••••••••••••••••••"
  }

  const formatDate = (date: Date) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true })
  }

  return (
    <TableRow>
      <TableCell>
        <div className="flex items-center space-x-2">
          <code className="text-sm font-mono bg-muted px-2 py-1 rounded break-all">
            {formatKey(keyData.key)}
          </code>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCopy(keyData.key, 'key')}
            className="h-6 w-6 p-0 shrink-0"
            title="Copy API Key"
          >
            <Copy className="w-3 h-3" />
          </Button>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center space-x-2">
          <code className="text-sm font-mono bg-muted px-2 py-1 rounded max-w-[150px] md:max-w-[200px] overflow-hidden break-all">
            {formatSecret(keyData.secret)}
          </code>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSecret(!showSecret)}
            className="h-6 w-6 p-0 shrink-0"
            title={showSecret ? "Hide Secret" : "Show Secret"}
          >
            {showSecret ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCopy(keyData.secret, 'secret')}
            className="h-6 w-6 p-0 shrink-0"
            title="Copy Secret"
          >
            <Copy className="w-3 h-3" />
          </Button>
        </div>
      </TableCell>
      <TableCell className="hidden md:table-cell">
        <div className="text-sm text-muted-foreground">
          {formatDate(keyData.createdAt)}
        </div>
      </TableCell>
      <TableCell className="hidden lg:table-cell">
        <div className="text-sm text-muted-foreground">
          {formatDate(keyData.updatedAt)}
        </div>
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onCopy(keyData.key, 'key')}>
              <Copy className="mr-2 h-4 w-4" />
              Copy Key
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onCopy(keyData.secret, 'secret')}>
              <Copy className="mr-2 h-4 w-4" />
              Copy Secret
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onRegenerate(keyData)}>
              <RotateCcw className="mr-2 h-4 w-4" />
              Regenerate Secret
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDelete(keyData)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  )
}

export function PhonebookKeysTable({ keys }: PhonebookKeysTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [keyToDelete, setKeyToDelete] = useState<PhonebookKey | null>(null)
  const [regenerateDialogOpen, setRegenerateDialogOpen] = useState(false)
  const [keyToRegenerate, setKeyToRegenerate] = useState<PhonebookKey | null>(null)
  const router = useRouter()
  const { toast } = useToast()

  const handleDelete = (keyData: PhonebookKey) => {
    setKeyToDelete(keyData)
    setDeleteDialogOpen(true)
  }

  const handleConfirmDelete = async (keyId: string) => {
    try {
      const response = await fetch(`/api/phonebook-keys?id=${keyId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        toast({
          title: "API Key Deleted",
          description: "The API key has been successfully deleted.",
        })
        router.refresh()
      } else {
        const errorData = await response.json()
        toast({
          title: "Delete Failed",
          description: errorData.error || "Failed to delete the API key. Please try again.",
          variant: "destructive",
        })
      }
    } catch {
      toast({
        title: "Delete Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleRegenerate = (keyData: PhonebookKey) => {
    setKeyToRegenerate(keyData)
    setRegenerateDialogOpen(true)
  }

  const handleConfirmRegenerate = async (keyId: string) => {
    try {
      const response = await fetch(`/api/phonebook-keys?id=${keyId}`, {
        method: 'PUT',
      })

      if (response.ok) {
        await response.json()
        toast({
          title: "Secret Regenerated",
          description: "A new secret has been generated for your API key.",
        })
        router.refresh()
      } else {
        const errorData = await response.json()
        toast({
          title: "Regeneration Failed",
          description: errorData.error || "Failed to regenerate the secret. Please try again.",
          variant: "destructive",
        })
      }
    } catch {
      toast({
        title: "Regeneration Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleCopy = async (text: string, type: 'key' | 'secret') => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied to Clipboard",
        description: `API ${type} has been copied to your clipboard.`,
      })
    } catch {
      // Fallback for browsers that don't support clipboard API
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        toast({
          title: "Copied to Clipboard",
          description: `API ${type} has been copied to your clipboard.`,
        })
      } catch {
        toast({
          title: "Copy Failed",
          description: "Unable to copy to clipboard. Please copy manually.",
          variant: "destructive",
        })
      }
    }
  }

  return (
    <>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>API Key</TableHead>
              <TableHead>Secret</TableHead>
              <TableHead className="hidden md:table-cell">Created</TableHead>
              <TableHead className="hidden lg:table-cell">Updated</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {keys.map((key) => (
              <KeyRow
                key={key.id}
                keyData={key}
                onDelete={handleDelete}
                onRegenerate={handleRegenerate}
                onCopy={handleCopy}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      <DeleteKeyDialog
        keyData={keyToDelete}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
      />

      <RegenerateSecretDialog
        keyData={keyToRegenerate}
        open={regenerateDialogOpen}
        onOpenChange={setRegenerateDialogOpen}
        onConfirm={handleConfirmRegenerate}
      />
    </>
  )
}
