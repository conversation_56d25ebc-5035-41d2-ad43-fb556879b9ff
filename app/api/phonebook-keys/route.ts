import { NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/withAuth';
import {
  createPhonebookKey,
  getUserPhonebookKeys,
  deletePhonebookKey,
  regeneratePhonebookSecret
} from '@/lib/phonebook-keys';

/**
 * GET /api/phonebook-keys
 * Get all phonebook keys for the authenticated user
 */
export const GET = withAuth(async (_request, { user }) => {
  try {
    const keys = await getUserPhonebookKeys(user.id);

    return NextResponse.json({
      keys: keys.map(key => ({
        id: key.id,
        key: key.key,
        secret: key.secret,
        createdAt: key.createdAt,
        updatedAt: key.updatedAt,
      })),
    });
  } catch (error) {
    console.error('Error fetching phonebook keys:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
});

/**
 * POST /api/phonebook-keys
 * Create a new phonebook key for the authenticated user
 */
export const POST = withAuth(async (_request, { user }) => {
  try {
    const newKey = await createPhonebookKey(user.id);

    if (!newKey) {
      return NextResponse.json(
        { error: 'Failed to create phonebook key' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      key: {
        id: newKey.id,
        key: newKey.key,
        secret: newKey.secret,
        createdAt: newKey.createdAt,
        updatedAt: newKey.updatedAt,
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating phonebook key:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
});

/**
 * DELETE /api/phonebook-keys
 * Delete a phonebook key
 */
export const DELETE = withAuth(async (request, { user }) => {
  try {
    const { searchParams } = new URL(request.url);
    const keyId = searchParams.get('id');

    if (!keyId) {
      return NextResponse.json(
        { error: 'Missing key ID' },
        { status: 400 }
      );
    }

    const success = await deletePhonebookKey(keyId, user.id);

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to delete phonebook key' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting phonebook key:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
});

/**
 * PUT /api/phonebook-keys
 * Regenerate secret for a phonebook key
 */
export const PUT = withAuth(async (request, { user }) => {
  try {
    const { searchParams } = new URL(request.url);
    const keyId = searchParams.get('id');

    if (!keyId) {
      return NextResponse.json(
        { error: 'Missing key ID' },
        { status: 400 }
      );
    }

    const newSecret = await regeneratePhonebookSecret(keyId, user.id);

    if (!newSecret) {
      return NextResponse.json(
        { error: 'Failed to regenerate secret' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      secret: newSecret,
    });
  } catch (error) {
    console.error('Error regenerating phonebook secret:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
});
